#!/usr/bin/env python3
"""
Document Validation with Azure API Management
============================================
This script runs document validation using your Azure API Management endpoint.
"""

import os
import asyncio

# Set your Azure API Management configuration
# (Update these with the same values you used in test_azure_config.py)
SUBSCRIPTION_KEY = "your-actual-subscription-key-here"  # Update this
API_URL = "https://your-actual-api-url.com/endpoint"    # Update this

# Set environment variables before importing
os.environ['SUBSCRIPTION_KEY'] = SUBSCRIPTION_KEY
os.environ['API_URL'] = API_URL

from Document_Validation_Agentic import DocumentValidationAgent, auto_detect_files

async def validate_documents_with_azure():
    """Run document validation using Azure API Management"""
    print("🚀 DOCUMENT VALIDATION WITH AZURE API MANAGEMENT")
    print("=" * 70)
    
    try:
        # Initialize validation agent with Azure API
        print("🔧 Initializing Document Validation Agent with Azure API...")
        agent = DocumentValidationAgent(model_name="gpt-4o", offline_mode=False)
        
        # Auto-detect PDF files
        print("\n🔍 Auto-detecting PDF files...")
        sample_pdf, generated_pdf = auto_detect_files()
        
        if not sample_pdf or not generated_pdf:
            print("❌ Could not find PDF files for validation")
            print("💡 Make sure you have PDFs in sample/ and generated/ folders")
            return False
            
        print(f"✅ Sample PDF: {os.path.basename(sample_pdf)}")
        print(f"✅ Generated PDF: {os.path.basename(generated_pdf)}")
        
        # Run validation with Azure API
        print(f"\n📄 Starting validation using Azure API Management...")
        print("🔑 Using your custom ask_gpt4_about_claim API pattern")
        print("=" * 70)
        
        result = await agent.validate_documents(sample_pdf, generated_pdf)
        
        # Generate and display report
        print("\n📊 GENERATING VALIDATION REPORT...")
        report = agent.generate_report(result)
        
        print("\n" + "=" * 70)
        print("📄 VALIDATION RESULTS WITH AZURE API:")
        print("=" * 70)
        print(report)
        
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    # Check if configuration is updated
    if SUBSCRIPTION_KEY == "your-actual-subscription-key-here":
        print("⚠️  CONFIGURATION NEEDED")
        print("=" * 50)
        print("Please update the SUBSCRIPTION_KEY and API_URL values in this script")
        print("Use the same values you used in test_azure_config.py")
        print()
        print("Look for these lines at the top of the script:")
        print("SUBSCRIPTION_KEY = 'your-actual-subscription-key-here'")
        print("API_URL = 'https://your-actual-api-url.com/endpoint'")
        return
    
    # Run validation
    success = await validate_documents_with_azure()
    
    if success:
        print("\n🎉 SUCCESS! Document validation completed using Azure API Management")
        print("✅ Your ask_gpt4_about_claim function is now powering the AI analysis!")
    else:
        print("\n❌ Validation failed. Check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
