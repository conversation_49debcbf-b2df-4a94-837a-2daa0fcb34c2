#!/usr/bin/env python3
"""
Quick Azure API Management Test
==============================
Update the configuration below with your actual values and run this script.
"""

import os
import asyncio

# 🔧 UPDATE THESE VALUES WITH YOUR ACTUAL CONFIGURATION
# =====================================================
SUBSCRIPTION_KEY = "your-actual-subscription-key-here"
API_URL = "https://your-actual-api-url.com/endpoint"

# Set environment variables
os.environ['SUBSCRIPTION_KEY'] = SUBSCRIPTION_KEY
os.environ['API_URL'] = API_URL

# Import after setting environment variables
from Document_Validation_Agentic import test_openai_connection, DocumentValidationAgent

async def test_configuration():
    """Test your Azure API Management configuration"""
    print("🧪 TESTING AZURE API MANAGEMENT CONFIGURATION")
    print("=" * 60)
    
    print(f"🔑 Subscription Key: {'*' * (len(SUBSCRIPTION_KEY) - 4)}{SUBSCRIPTION_KEY[-4:]}")
    print(f"🌐 API URL: {API_URL}")
    print()
    
    # Test the connection
    print("🔗 Testing API connection...")
    connection_ok = await test_openai_connection()
    
    if connection_ok:
        print("\n✅ SUCCESS! Your Azure API Management is working!")
        print("🎉 You can now use the Document Validation system with your custom API")
        
        # Test creating the validation agent
        try:
            agent = DocumentValidationAgent(model_name="gpt-4o", offline_mode=False)
            print("✅ Document Validation Agent initialized successfully")
            print("📄 Ready to validate documents!")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize validation agent: {e}")
            return False
    else:
        print("\n❌ API connection failed!")
        print("💡 Please check your SUBSCRIPTION_KEY and API_URL values")
        return False

if __name__ == "__main__":
    print("🚀 AZURE API MANAGEMENT INTEGRATION TEST")
    print("=" * 60)
    
    if SUBSCRIPTION_KEY == "your-actual-subscription-key-here":
        print("⚠️  CONFIGURATION NEEDED")
        print("Please update the SUBSCRIPTION_KEY and API_URL values in this script")
        print("Look for the section marked '🔧 UPDATE THESE VALUES'")
    else:
        asyncio.run(test_configuration())
